import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

struct EnhancedPlayerAvatar: View {
    let player: Player
    let size: CGFloat
    let showCrown: Bool

    @State private var processedImage: UIImage?
    @State private var isProcessing = false

    init(player: Player, size: CGFloat = 120, showCrown: Bool = false) {
        self.player = player
        self.size = size
        self.showCrown = showCrown
    }
    
    var body: some View {
        // Calculate crown height to ensure proper spacing
        let crownHeight = showCrown ? size * 0.25 : 0

        VStack(spacing: 0) {
            // Crown space (invisible but reserves space)
            if showCrown {
                Spacer()
                    .frame(height: crownHeight)
            }

            // Main avatar content
            ZStack {
                if let processedImage = processedImage {
                    // Show processed image with background removed - larger and rectangular
                    Image(uiImage: processedImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: size, height: size)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                        )
                        .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
                } else if let imageData = player.profileImageData,
                          let uiImage = UIImage(data: imageData) {
                    // Show original image while processing or if processing failed
                    ZStack {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: size, height: size)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                            )
                            .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)

                        if isProcessing {
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                                .frame(width: size, height: size)

                            VStack(spacing: 8) {
                                ProgressView()
                                    .scaleEffect(1.2)
                                Text("Processing...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .onAppear {
                        processImageForBackgroundRemoval(uiImage)
                    }
                } else {
                    // Fallback to initials - keep circular for consistency
                    Circle()
                        .fill(showCrown ?
                              Color(red: 0.95, green: 0.90, blue: 0.75).gradient :
                              Color.blue.gradient)
                        .frame(width: size, height: size)
                        .overlay(
                            Text(String(player.name.prefix(1)).uppercased())
                                .font(fontForSize(size))
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )
                        .overlay(
                            Circle()
                                .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                        )
                        .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
                }

                // Crown for #1 player - positioned above the avatar
                if showCrown {
                    Image(systemName: "crown.fill")
                        .font(crownFontForSize(size))
                        .foregroundColor(.yellow)
                        .shadow(color: .orange, radius: 3)
                        .offset(y: -size * 0.5 - crownHeight * 0.5)
                }
            }
        }
        .frame(height: size + crownHeight) // Ensure total height includes crown space
    }
    
    private func processImageForBackgroundRemoval(_ image: UIImage) {
        guard !isProcessing else { return }
        
        isProcessing = true
        
        Task {
            let processedImage = await removeBackground(from: image)
            
            await MainActor.run {
                self.processedImage = processedImage
                self.isProcessing = false
            }
        }
    }
    
    private func removeBackground(from image: UIImage) async -> UIImage? {
        // First normalize the image orientation using UIGraphicsContext
        guard let normalizedImage = normalizeImageOrientation(image) else { return nil }
        guard let inputImage = CIImage(image: normalizedImage) else { return nil }

        return await withCheckedContinuation { continuation in
            Task {
                do {
                    guard let maskImage = try await createMask(from: inputImage) else {
                        continuation.resume(returning: nil)
                        return
                    }

                    let outputImage = applyMask(mask: maskImage, to: inputImage)
                    let finalImage = convertToUIImage(ciImage: outputImage)
                    continuation.resume(returning: finalImage)
                } catch {
                    print("Background removal failed: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    private func createMask(from inputImage: CIImage) async throws -> CIImage? {
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: inputImage)

        try handler.perform([request])

        if let result = request.results?.first {
            let mask = try result.generateScaledMaskForImage(forInstances: result.allInstances, from: handler)
            return CIImage(cvPixelBuffer: mask)
        }

        return nil
    }

    private func applyMask(mask: CIImage, to image: CIImage) -> CIImage {
        let filter = CIFilter.blendWithMask()

        filter.inputImage = image
        filter.maskImage = mask
        filter.backgroundImage = CIImage.empty()

        return filter.outputImage ?? image
    }

    private func convertToUIImage(ciImage: CIImage) -> UIImage {
        guard let cgImage = CIContext(options: nil).createCGImage(ciImage, from: ciImage.extent) else {
            return UIImage() // Return empty image as fallback
        }

        return UIImage(cgImage: cgImage)
    }

    private func createOrientedCIImage(from image: UIImage) -> CIImage? {
        guard let cgImage = image.cgImage else { return nil }

        // Create CIImage from CGImage
        let ciImage = CIImage(cgImage: cgImage)

        // Convert UIImage.Orientation to CGImagePropertyOrientation
        let cgOrientation = cgImageOrientation(from: image.imageOrientation)

        // Apply orientation transform
        let orientedImage = ciImage.oriented(cgOrientation)

        return orientedImage
    }

    private func cgImageOrientation(from uiOrientation: UIImage.Orientation) -> CGImagePropertyOrientation {
        switch uiOrientation {
        case .up:
            return .up
        case .down:
            return .down
        case .left:
            return .left
        case .right:
            return .right
        case .upMirrored:
            return .upMirrored
        case .downMirrored:
            return .downMirrored
        case .leftMirrored:
            return .leftMirrored
        case .rightMirrored:
            return .rightMirrored
        @unknown default:
            return .up
        }
    }

    private func normalizeImageOrientation(_ image: UIImage) -> UIImage? {
        // If the image is already in the correct orientation, return it as-is
        if image.imageOrientation == .up {
            return image
        }

        // Get the correct size for the normalized image
        let size = image.size

        // Create a graphics context to redraw the image in the correct orientation
        UIGraphicsBeginImageContextWithOptions(size, false, image.scale)
        defer { UIGraphicsEndImageContext() }

        // Draw the image which will automatically apply the orientation transform
        image.draw(in: CGRect(origin: .zero, size: size))

        // Get the normalized image
        guard let normalizedImage = UIGraphicsGetImageFromCurrentImageContext() else {
            return nil
        }

        // Create a new UIImage with .up orientation to ensure it's properly normalized
        guard let cgImage = normalizedImage.cgImage else { return nil }
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: .up)
    }
    
    private func fontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .headline
        case 40..<80:
            return .title2
        case 80..<120:
            return .title
        default:
            return .largeTitle
        }
    }
    
    private func crownFontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .caption
        case 40..<80:
            return .title3
        case 80..<120:
            return .title2
        default:
            return .title
        }
    }
}

#Preview {
    let sampleCompetitionId = UUID()
    
    VStack(spacing: 20) {
        // Player without photo
        EnhancedPlayerAvatar(player: Player(name: "John Doe", competitionId: sampleCompetitionId), size: 120)
        
        // Player with crown
        EnhancedPlayerAvatar(player: Player(name: "Jane Smith", competitionId: sampleCompetitionId), size: 120, showCrown: true)
        
        // Different sizes
        HStack(spacing: 15) {
            EnhancedPlayerAvatar(player: Player(name: "Small", competitionId: sampleCompetitionId), size: 60)
            EnhancedPlayerAvatar(player: Player(name: "Medium", competitionId: sampleCompetitionId), size: 90)
            EnhancedPlayerAvatar(player: Player(name: "Large", competitionId: sampleCompetitionId), size: 120)
        }
    }
    .padding()
}
