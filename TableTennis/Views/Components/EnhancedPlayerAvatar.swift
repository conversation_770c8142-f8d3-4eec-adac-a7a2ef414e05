import SwiftUI
import Vision

struct EnhancedPlayerAvatar: View {
    let player: Player
    let size: CGFloat
    let showCrown: Bool
    
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    
    init(player: Player, size: CGFloat = 120, showCrown: Bool = false) {
        self.player = player
        self.size = size
        self.showCrown = showCrown
    }
    
    var body: some View {
        ZStack {
            if let processedImage = processedImage {
                // Show processed image with background removed
                Image(uiImage: processedImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size, height: size)
                    .clipShape(Circle())
                    .overlay(
                        Circle()
                            .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                    )
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            } else if let imageData = player.profileImageData,
                      let uiImage = UIImage(data: imageData) {
                // Show original image while processing or if processing failed
                ZStack {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: size, height: size)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                        )
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                    
                    if isProcessing {
                        Circle()
                            .fill(.ultraThinMaterial)
                            .frame(width: size, height: size)
                        
                        ProgressView()
                            .scaleEffect(1.2)
                    }
                }
                .onAppear {
                    processImageForBackgroundRemoval(uiImage)
                }
            } else {
                // Fallback to initials
                Circle()
                    .fill(showCrown ?
                          Color(red: 0.95, green: 0.90, blue: 0.75).gradient :
                          Color.blue.gradient)
                    .frame(width: size, height: size)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(fontForSize(size))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                    .overlay(
                        Circle()
                            .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                    )
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
            }
            
            // Crown for #1 player
            if showCrown {
                Image(systemName: "crown.fill")
                    .font(crownFontForSize(size))
                    .foregroundColor(.yellow)
                    .shadow(color: .orange, radius: 3)
                    .offset(y: -size * 0.55)
            }
        }
    }
    
    private func processImageForBackgroundRemoval(_ image: UIImage) {
        guard !isProcessing else { return }
        
        isProcessing = true
        
        Task {
            let processedImage = await removeBackground(from: image)
            
            await MainActor.run {
                self.processedImage = processedImage
                self.isProcessing = false
            }
        }
    }
    
    private func removeBackground(from image: UIImage) async -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }
        
        return await withCheckedContinuation { continuation in
            let request = VNGeneratePersonSegmentationRequest { request, error in
                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let maskedImage = self.applyMask(to: image, mask: observation.pixelBuffer)
                continuation.resume(returning: maskedImage)
            }
            
            request.qualityLevel = .balanced
            request.outputPixelFormat = kCVPixelFormatType_OneComponent8
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                print("Background removal failed: \(error)")
                continuation.resume(returning: nil)
            }
        }
    }
    
    private func applyMask(to image: UIImage, mask: CVPixelBuffer) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let ciImage = CIImage(cgImage: cgImage)
        let maskImage = CIImage(cvPixelBuffer: mask)

        // Use CIBlendWithMask filter instead
        guard let filter = CIFilter(name: "CIBlendWithMask") else { return nil }
        filter.setValue(ciImage, forKey: kCIInputImageKey)
        filter.setValue(CIImage.empty(), forKey: kCIInputBackgroundImageKey)
        filter.setValue(maskImage, forKey: kCIInputMaskImageKey)

        guard let outputImage = filter.outputImage else { return nil }

        let context = CIContext()
        guard let outputCGImage = context.createCGImage(outputImage, from: outputImage.extent) else { return nil }

        return UIImage(cgImage: outputCGImage)
    }
    
    private func fontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .headline
        case 40..<80:
            return .title2
        case 80..<120:
            return .title
        default:
            return .largeTitle
        }
    }
    
    private func crownFontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .caption
        case 40..<80:
            return .title3
        case 80..<120:
            return .title2
        default:
            return .title
        }
    }
}

#Preview {
    let sampleCompetitionId = UUID()
    
    VStack(spacing: 20) {
        // Player without photo
        EnhancedPlayerAvatar(player: Player(name: "John Doe", competitionId: sampleCompetitionId), size: 120)
        
        // Player with crown
        EnhancedPlayerAvatar(player: Player(name: "Jane Smith", competitionId: sampleCompetitionId), size: 120, showCrown: true)
        
        // Different sizes
        HStack(spacing: 15) {
            EnhancedPlayerAvatar(player: Player(name: "Small", competitionId: sampleCompetitionId), size: 60)
            EnhancedPlayerAvatar(player: Player(name: "Medium", competitionId: sampleCompetitionId), size: 90)
            EnhancedPlayerAvatar(player: Player(name: "Large", competitionId: sampleCompetitionId), size: 120)
        }
    }
    .padding()
}
