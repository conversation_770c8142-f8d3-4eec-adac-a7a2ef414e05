import SwiftUI
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

struct EnhancedPlayerAvatar: View {
    let player: Player
    let size: CGFloat
    let showCrown: Bool

    @State private var processedImage: UIImage?
    @State private var isProcessing = false

    init(player: Player, size: CGFloat = 120, showCrown: Bool = false) {
        self.player = player
        self.size = size
        self.showCrown = showCrown
    }
    
    var body: some View {
        ZStack {
            if let processedImage = processedImage {
                // Show processed image with background removed - larger and rectangular
                Image(uiImage: processedImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: size, height: size)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                    )
                    .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
            } else if let imageData = player.profileImageData,
                      let uiImage = UIImage(data: imageData) {
                // Show original image while processing or if processing failed
                ZStack {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: size, height: size)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                        )
                        .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)

                    if isProcessing {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(.ultraThinMaterial)
                            .frame(width: size, height: size)

                        VStack(spacing: 8) {
                            ProgressView()
                                .scaleEffect(1.2)
                            Text("Processing...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .onAppear {
                    processImageForBackgroundRemoval(uiImage)
                }
            } else {
                // Fallback to initials - keep circular for consistency
                Circle()
                    .fill(showCrown ?
                          Color(red: 0.95, green: 0.90, blue: 0.75).gradient :
                          Color.blue.gradient)
                    .frame(width: size, height: size)
                    .overlay(
                        Text(String(player.name.prefix(1)).uppercased())
                            .font(fontForSize(size))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    )
                    .overlay(
                        Circle()
                            .stroke(showCrown ? Color.yellow : Color.gray.opacity(0.3), lineWidth: showCrown ? 3 : 2)
                    )
                    .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
            }

            // Crown for #1 player
            if showCrown {
                Image(systemName: "crown.fill")
                    .font(crownFontForSize(size))
                    .foregroundColor(.yellow)
                    .shadow(color: .orange, radius: 3)
                    .offset(y: -size * 0.55)
            }
        }
    }
    
    private func processImageForBackgroundRemoval(_ image: UIImage) {
        guard !isProcessing else { return }
        
        isProcessing = true
        
        Task {
            let processedImage = await removeBackground(from: image)
            
            await MainActor.run {
                self.processedImage = processedImage
                self.isProcessing = false
            }
        }
    }
    
    private func removeBackground(from image: UIImage) async -> UIImage? {
        // Create CIImage with proper orientation handling
        guard let inputImage = createOrientedCIImage(from: image) else { return nil }

        return await withCheckedContinuation { continuation in
            Task {
                do {
                    guard let maskImage = try await createMask(from: inputImage) else {
                        continuation.resume(returning: nil)
                        return
                    }

                    let outputImage = applyMask(mask: maskImage, to: inputImage)
                    let finalImage = convertToUIImage(ciImage: outputImage)
                    continuation.resume(returning: finalImage)
                } catch {
                    print("Background removal failed: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    private func createMask(from inputImage: CIImage) async throws -> CIImage? {
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: inputImage)

        try handler.perform([request])

        if let result = request.results?.first {
            let mask = try result.generateScaledMaskForImage(forInstances: result.allInstances, from: handler)
            return CIImage(cvPixelBuffer: mask)
        }

        return nil
    }

    private func applyMask(mask: CIImage, to image: CIImage) -> CIImage {
        let filter = CIFilter.blendWithMask()

        filter.inputImage = image
        filter.maskImage = mask
        filter.backgroundImage = CIImage.empty()

        return filter.outputImage ?? image
    }

    private func convertToUIImage(ciImage: CIImage) -> UIImage {
        guard let cgImage = CIContext(options: nil).createCGImage(ciImage, from: ciImage.extent) else {
            return UIImage() // Return empty image as fallback
        }

        return UIImage(cgImage: cgImage)
    }

    private func createOrientedCIImage(from image: UIImage) -> CIImage? {
        guard let cgImage = image.cgImage else { return nil }

        // Create CIImage from CGImage with proper orientation
        let ciImage = CIImage(cgImage: cgImage)

        // Apply orientation transform if needed
        let orientedImage = ciImage.oriented(forExifOrientation: Int32(image.imageOrientation.rawValue))

        return orientedImage
    }

    private func normalizeImageOrientation(_ image: UIImage) -> UIImage? {
        // If the image is already in the correct orientation, return it as-is
        if image.imageOrientation == .up {
            return image
        }

        // Create a graphics context to redraw the image in the correct orientation
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        defer { UIGraphicsEndImageContext() }

        image.draw(in: CGRect(origin: .zero, size: image.size))
        return UIGraphicsGetImageFromCurrentImageContext()
    }
    
    private func fontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .headline
        case 40..<80:
            return .title2
        case 80..<120:
            return .title
        default:
            return .largeTitle
        }
    }
    
    private func crownFontForSize(_ size: CGFloat) -> Font {
        switch size {
        case 0..<40:
            return .caption
        case 40..<80:
            return .title3
        case 80..<120:
            return .title2
        default:
            return .title
        }
    }
}

#Preview {
    let sampleCompetitionId = UUID()
    
    VStack(spacing: 20) {
        // Player without photo
        EnhancedPlayerAvatar(player: Player(name: "John Doe", competitionId: sampleCompetitionId), size: 120)
        
        // Player with crown
        EnhancedPlayerAvatar(player: Player(name: "Jane Smith", competitionId: sampleCompetitionId), size: 120, showCrown: true)
        
        // Different sizes
        HStack(spacing: 15) {
            EnhancedPlayerAvatar(player: Player(name: "Small", competitionId: sampleCompetitionId), size: 60)
            EnhancedPlayerAvatar(player: Player(name: "Medium", competitionId: sampleCompetitionId), size: 90)
            EnhancedPlayerAvatar(player: Player(name: "Large", competitionId: sampleCompetitionId), size: 120)
        }
    }
    .padding()
}
