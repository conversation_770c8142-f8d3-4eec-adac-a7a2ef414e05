import SwiftUI

struct EnhancedPlayerAvatarTestView: View {
    let sampleCompetitionId = UUID()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 30) {
                    Text("Enhanced Player Avatar Test")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("This view demonstrates the new enhanced player avatar with background removal functionality.")
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    VStack(spacing: 20) {
                        Text("Different Sizes")
                            .font(.headline)
                        
                        HStack(spacing: 20) {
                            VStack {
                                EnhancedPlayerAvatar(
                                    player: Player(name: "Small", competitionId: sampleCompetitionId),
                                    size: 60
                                )
                                Text("60pt")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            VStack {
                                EnhancedPlayerAvatar(
                                    player: Player(name: "Medium", competitionId: sampleCompetitionId),
                                    size: 90
                                )
                                Text("90pt")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            VStack {
                                EnhancedPlayerAvatar(
                                    player: Player(name: "Large", competitionId: sampleCompetitionId),
                                    size: 120
                                )
                                Text("120pt")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    VStack(spacing: 20) {
                        Text("With Crown (Top Player)")
                            .font(.headline)
                        
                        EnhancedPlayerAvatar(
                            player: Player(name: "Champion", competitionId: sampleCompetitionId),
                            size: 120,
                            showCrown: true
                        )
                    }
                    
                    VStack(spacing: 20) {
                        Text("Comparison")
                            .font(.headline)
                        
                        HStack(spacing: 30) {
                            VStack {
                                PlayerAvatar(
                                    player: Player(name: "Original", competitionId: sampleCompetitionId),
                                    size: 80
                                )
                                Text("Original Avatar")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            VStack {
                                EnhancedPlayerAvatar(
                                    player: Player(name: "Enhanced", competitionId: sampleCompetitionId),
                                    size: 120
                                )
                                Text("Enhanced Avatar")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    VStack(spacing: 20) {
                        Text("Large Detail View Size")
                            .font(.headline)

                        EnhancedPlayerAvatar(
                            player: Player(name: "Detail", competitionId: sampleCompetitionId),
                            size: 200,
                            showCrown: true
                        )
                        Text("200pt (Detail View)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Text("Note: Background removal works with photos containing people/objects. Photos are shown in rounded rectangles, while initials remain circular. The background removal uses iOS 17+ Vision framework.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .padding(.top, 20)
                }
                .padding()
            }
            .navigationTitle("Avatar Test")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    EnhancedPlayerAvatarTestView()
}
